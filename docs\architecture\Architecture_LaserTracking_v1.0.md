# 激光追踪系统架构文档

**版本**: v1.0  
**日期**: 2025-01-26  
**项目**: 2023年电赛E题激光追踪系统  
**作者**: 系统架构团队  

## 1. 项目概述

### 1.1 项目背景
本项目是2023年全国大学生电子设计竞赛E题的激光追踪系统，主要功能是使用步进电机控制的云台实现对目标激光点的高精度自动追踪。

### 1.2 系统目标
- 实现激光点的实时识别和追踪
- 提供亚像素级的追踪精度
- 保证系统的实时响应性能
- 支持多传感器融合控制

### 1.3 技术特点
- **高精度控制**: 步进电机+闭环PID控制
- **实时性能**: 1ms数据处理+20ms控制周期
- **模块化设计**: BSP层/APP层清晰分离
- **多传感器融合**: 视觉+姿态传感器

## 2. 硬件架构

### 2.1 硬件组成
```
激光追踪系统硬件架构
├── 主控制器
│   └── STM32F407VET6 (ARM Cortex-M4, 168MHz)
├── 执行机构
│   ├── ZDT步进电机 (X轴) - UART2控制
│   └── ZDT步进电机 (Y轴) - UART4控制
├── 视觉系统
│   └── MaixCam/树莓派 - UART5/UART6通信
├── 传感器系统
│   ├── HWT101九轴传感器 - I2C接口
│   └── 编码器反馈系统
├── 显示系统
│   └── OLED显示屏 - I2C接口
└── 通信接口
    ├── UART1 - 调试串口
    ├── UART2 - X轴电机控制
    ├── UART4 - Y轴电机控制
    ├── UART5/UART6 - 视觉系统通信
    ├── I2C1 - 传感器通信
    └── I2C2 - 显示系统
```

### 2.2 硬件连接关系
- **STM32F407**: 系统核心控制器，负责整体协调
- **ZDT步进电机**: 双轴云台，提供精确的位置控制
- **MaixCam**: 视觉处理单元，负责激光点识别
- **HWT101**: 九轴姿态传感器，提供姿态反馈
- **OLED**: 系统状态显示

### 2.3 电源和信号分配
- **主电源**: 12V直流电源供电
- **控制信号**: 3.3V TTL电平
- **通信协议**: UART、I2C标准协议
- **电机驱动**: Emm_V5专用协议

## 3. 软件架构

### 3.1 软件层次结构
```
软件架构层次图
├── 应用层 (Application Layer)
│   ├── 主控制逻辑 (main.c)
│   ├── 任务调度器 (schedule.c)
│   └── 用户接口
├── 中间件层 (Middleware Layer)
│   ├── PID控制算法 (mypid.c)
│   ├── 数据处理模块
│   └── 通信协议栈
├── 驱动层 (Driver Layer)
│   ├── 步进电机驱动 (Emm_V5.c)
│   ├── 传感器驱动 (hwt101_driver.c)
│   └── 通信驱动
└── 硬件抽象层 (BSP Layer)
    ├── 步进电机BSP (step_motor_bsp.c)
    ├── 串口BSP (uart_bsp.c)
    ├── 传感器BSP (hwt101_bsp.c)
    ├── 视觉接口BSP (pi_bsp.c)
    └── 显示BSP (oled_bsp.c)
```

### 3.2 模块功能说明

#### 3.2.1 BSP层 (Board Support Package)
- **step_motor_bsp**: 步进电机底层控制接口
- **uart_bsp**: 串口通信管理和数据缓冲
- **pi_bsp**: 树莓派/MaixCam通信接口
- **hwt101_bsp**: 姿态传感器接口
- **schedule**: 任务调度和时间管理

#### 3.2.2 APP层 (Application Layer)
- **Emm_V5**: ZDT步进电机通信协议实现
- **mypid**: PID控制算法实现
- **hwt101_driver**: 传感器数据处理驱动
- **motor_driver**: 电机控制抽象层

### 3.3 数据流程图
```
数据流程示意图
[MaixCam视觉系统] 
    ↓ UART通信
[串口接收缓冲区] 
    ↓ 数据解析
[激光坐标提取] → [red:(x,y), gre:(x,y)]
    ↓ 坐标处理
[PID控制器] → [计算控制量]
    ↓ 控制指令
[Emm_V5协议] → [步进电机驱动]
    ↓ 执行动作
[云台运动] → [激光追踪]
    ↑ 位置反馈
[编码器/传感器反馈]
```

## 4. 接口定义

### 4.1 BSP层接口

#### 4.1.1 步进电机接口
```c
// 步进电机初始化
void Step_Motor_Init(void);

// 设置电机速度 (RPM)
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm);

// 设置电机位置 (脉冲数)
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance);

// 停止电机
void Step_Motor_Stop(void);
```

#### 4.1.2 视觉通信接口
```c
// 数据解析函数
int pi_parse_data(char *buffer);

// 视觉处理任务
void pi_proc(void);

// 激光坐标结构体
typedef struct {
    char type;        // 'R'红色, 'G'绿色
    int x, y;         // 坐标值
    uint8_t isValid;  // 有效标志
} LaserCoord_t;
```

#### 4.1.3 PID控制接口
```c
// PID结构体初始化
void PID_struct_init(pid_t *pid, uint32_t mode, 
                     uint32_t maxout, uint32_t intergral_limit,
                     float kp, float ki, float kd);

// PID计算
float pid_calc(pid_t *pid, float get, float set, uint8_t smooth);
```

### 4.2 通信协议定义

#### 4.2.1 Emm_V5步进电机协议
- **命令格式**: [地址][功能码][数据][校验]
- **速度控制**: 支持0.1RPM精度
- **位置控制**: 支持绝对/相对位置控制
- **状态查询**: 实时位置、速度、温度等

#### 4.2.2 视觉数据协议
- **红色激光**: `red:(x,y)\n`
- **绿色激光**: `gre:(x,y)\n`
- **坐标范围**: 像素坐标系
- **更新频率**: 实时更新

## 5. 系统集成方案

### 5.1 STM32F407集成
- **时钟配置**: 168MHz主频，确保实时性能
- **外设配置**: 多路UART、I2C、Timer配置
- **中断管理**: 串口接收中断、定时器中断
- **内存管理**: 环形缓冲区管理数据流

### 5.2 ZDT步进电机集成
- **通信接口**: UART2(X轴)、UART4(Y轴)
- **控制协议**: Emm_V5专用协议
- **控制模式**: 速度控制为主，位置控制为辅
- **参数配置**: 最大速度3RPM，加速度0

### 5.3 MaixCam视觉系统集成
- **通信方式**: UART5/UART6串口通信
- **数据格式**: 文本格式坐标数据
- **处理流程**: 图像采集→激光点识别→坐标计算→数据发送
- **实时性**: 配合STM32的20ms控制周期

### 5.4 多传感器融合
- **主传感器**: MaixCam视觉系统
- **辅助传感器**: HWT101姿态传感器
- **数据融合**: 坐标补偿和滤波处理
- **容错机制**: 传感器故障检测和切换

## 6. 性能指标

### 6.1 控制精度
- **追踪精度**: 亚像素级精度
- **位置精度**: ±0.1RPM速度控制精度
- **响应时间**: 20ms控制周期
- **稳态误差**: <1像素

### 6.2 实时性能
- **数据处理**: 1ms串口数据处理
- **控制周期**: 20ms PID控制周期
- **通信延迟**: <5ms视觉数据传输
- **系统延迟**: <50ms端到端延迟

### 6.3 可靠性指标
- **通信可靠性**: 校验和验证机制
- **系统稳定性**: 连续运行>2小时
- **故障恢复**: 自动重启和错误恢复
- **环境适应**: 室内光照条件下稳定工作

## 7. 扩展性设计

### 7.1 硬件扩展
- **传感器接口**: 预留I2C接口
- **通信接口**: 多余UART接口可扩展
- **电源管理**: 支持更大功率电机
- **机械接口**: 标准化安装接口

### 7.2 软件扩展
- **算法扩展**: 模块化PID支持多种算法
- **协议扩展**: 标准化通信接口
- **功能扩展**: 支持多目标追踪
- **参数调试**: 在线参数调整功能

## 8. 总结

本激光追踪系统采用了先进的模块化设计理念，通过STM32F407主控制器、ZDT步进电机云台和MaixCam视觉系统的有机结合，实现了高精度、高实时性的激光追踪功能。系统架构清晰，接口标准化，具有良好的可扩展性和可维护性，充分体现了现代嵌入式系统在精密控制领域的技术水平。

---
**文档版本历史**
- v1.0 (2025-01-26): 初始版本，完整系统架构设计